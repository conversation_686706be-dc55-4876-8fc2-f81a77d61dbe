# ============================================================================
# SPLASH SCREEN CONFIGURATION
# ============================================================================
# This configuration generates native splash screens for Android, iOS, and Web
#
# QUICK SETUP:
# 1. Modify the settings below as needed
# 2. Run: flutter pub run flutter_native_splash:create
# 3. To remove: flutter pub run flutter_native_splash:remove
#
# CURRENT SETTINGS:
# - Logo: ATlogo-white-splash.png (120x120 pixels)
# - Background: #FFB522 (orange)
# - Position: Centered on all platforms
# ============================================================================

flutter_native_splash:

  # SPLASH SCREEN CONFIGURATION
  # Background color for the splash screen
  color: "#FFB522"

  # Logo image for the splash screen (120x120 pixels, centered)
  # This image will be used across all platforms for consistency
  image: assets/images/ATlogo-white-splash.png

  # OPTIONAL CONFIGURATIONS (currently disabled for simplicity)
  # Uncomment and configure these options if needed:

  # Dark mode support:
  # color_dark: "#042a49"
  # image_dark: assets/images/ATlogo-dark-splash.png

  # Branding image (additional logo at bottom):
  # branding: assets/images/branding.png
  # branding_mode: bottom

  # ANDROID 12+ CONFIGURATION
  # Android 12 handles the splash screen differently than previous versions.
  # Please visit https://developer.android.com/guide/topics/ui/splash-screen
  android_12:
    # Use the same logo image for Android 12+ (optimized for circular display)
    image: assets/images/ATlogo-white-splash.png

    # Background color for Android 12+ splash screen
    color: "#FFB522"

    # Android 12+ dark mode support (optional):
    # image_dark: assets/images/ATlogo-dark-splash.png
    # color_dark: "#042a49"

  # PLATFORM CONTROL
  # Enable splash screen generation for all platforms
  android: true
  ios: true
  web: true

  # PLATFORM POSITIONING AND DISPLAY SETTINGS
  # Use unified logo across all platforms for consistency
  # Platform-specific image overrides are disabled to ensure consistent 120x120 logo display

  # LOGO POSITIONING CONFIGURATION
  # Center the logo on all platforms for consistent display
  android_gravity: center
  ios_content_mode: center
  web_image_mode: center

  # FLEXIBLE CONFIGURATION OPTIONS
  # Uncomment to customize further:
  # android_screen_orientation: portrait  # Lock orientation if needed

  # ADDITIONAL SETTINGS
  # Keep notification bar visible for better user experience
  fullscreen: false

  # iOS Info.plist configuration for smooth splash screen transition
  info_plist_files:
    - 'ios/Runner/Info.plist'
