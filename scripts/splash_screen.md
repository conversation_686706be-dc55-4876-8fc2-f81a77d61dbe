# Splash Screen Configuration Guide

## Overview

KOC-App sử dụng `flutter_native_splash` package để hiển thị splash screen trong quá trình khởi động app. Splash screen hiển thị logo AT trên nền màu cam #FFB522.

## C<PERSON>u Trúc Hiện Tại

### 1. Configuration File
- **File**: `flutter_native_splash.yaml`
- **Background color**: `#FFB522` (màu cam)
- **Logo**: `assets/images/ATlogo-white-splash.png` (960x960px, hiển thị 120x120px)
- **Position**: Centered trên tất cả platforms
- **Platforms**: Android, iOS, Web

### 2. Flutter Splash Screen Widget
- **File**: `lib/src/modules/splash/splash_screen.dart`
- **Duration**: 2 giây
- **Background**: Cùng màu với native splash (#FFB522)
- **Logo**: Cùng file với native splash để đảm bảo consistency

### 3. Generated Native Assets
- **Android**:
  - Splash images cho tất cả densities
  - Android 12+ compatibility
  - Launch background XML files
- **iOS**:
  - Launch images (1x, 2x, 3x scales)
  - LaunchScreen.storyboard integration
- **Web**:
  - Responsive splash images
  - Light/dark mode support

## Cách Tùy Chỉnh Splash Screen

### Thay Đổi Logo
1. Thay thế file `assets/images/ATlogo-white-splash.png` bằng logo mới (khuyến nghị 960x960px)
2. Chạy lệnh regenerate splash screen
3. Chạy script fix background (xem phần Troubleshooting)

### Thay Đổi Background Color
1. Sửa `color: "#FFB522"` trong `flutter_native_splash.yaml`
2. Sửa `backgroundColor: const Color(0xFFFFB522)` trong `splash_screen.dart`
3. Chạy lệnh regenerate splash screen
4. Chạy script fix background

### Thêm Dark Mode Support
Uncomment các dòng sau trong `flutter_native_splash.yaml`:
```yaml
color_dark: "#042a49"
image_dark: assets/images/ATlogo-dark-splash.png
```

## Commands

### Regenerate Splash Screen Assets
```bash
# Tạo lại splash screen assets
dart run flutter_native_splash:create

# QUAN TRỌNG: Sửa lỗi background Android (luôn chạy sau lệnh trên)
./scripts/fix_splash_background.sh

# Clean và rebuild
flutter clean
flutter pub get
```

## Troubleshooting

### ⚠️ Vấn Đề: Background Màu Nâu Trên Android

**Triệu chứng**: Splash screen trên Android real device hiển thị background màu nâu thay vì màu cam #FFB522.

**Nguyên nhân**: Package `flutter_native_splash` tạo ra bitmap background thay vì solid color, dẫn đến việc sử dụng file `background.png` có màu không đúng.

**Giải pháp**:
1. Chạy script fix tự động:
   ```bash
   ./scripts/fix_splash_background.sh
   ```

2. Hoặc sửa thủ công:
   - Mở `android/app/src/main/res/drawable/launch_background.xml`
   - Mở `android/app/src/main/res/drawable-v21/launch_background.xml`
   - Thay thế:
     ```xml
     <bitmap android:gravity="fill" android:src="@drawable/background"/>
     ```
   - Bằng:
     ```xml
     <shape android:shape="rectangle">
         <solid android:color="#FFB522"/>
     </shape>
     ```
   - Xóa file `background.png` trong cả hai thư mục drawable

### Script Fix Background

File `scripts/fix_splash_background.sh` tự động:
- Thay thế bitmap background bằng solid color #FFB522
- Xóa các file `background.png` không cần thiết
- Đảm bảo background nhất quán trên tất cả Android devices

**Lưu ý**: Luôn chạy script này sau mỗi lần chạy `dart run flutter_native_splash:create`.

## Testing

### Kiểm Tra Trên Devices
1. **Android**: Test trên cả Android < 12 và Android 12+
2. **iOS**: Test trên các iPhone với notch và không có notch
3. **Web**: Test trên các browsers khác nhau

### Checklist
- [ ] Background color đúng #FFB522 trên tất cả platforms
- [ ] Logo hiển thị centered và đúng kích thước 120x120
- [ ] Transition mượt từ native splash sang Flutter UI
- [ ] Android 12+ hiển thị logo trong circle đúng cách
- [ ] Dark mode hoạt động (nếu enabled)

## Files Liên Quan

```
flutter_native_splash.yaml          # Main configuration
lib/src/modules/splash/splash_screen.dart  # Flutter splash widget
scripts/fix_splash_background.sh    # Fix script cho Android
android/app/src/main/res/drawable/launch_background.xml
android/app/src/main/res/drawable-v21/launch_background.xml
android/app/src/main/res/values-v31/styles.xml
ios/Runner/Assets.xcassets/LaunchImage.imageset/
web/splash/img/
```

## Best Practices

1. **Consistency**: Sử dụng cùng logo và màu sắc cho cả native và Flutter splash
2. **Performance**: Logo size 960x960px cho quality tốt trên tất cả densities
3. **Maintenance**: Luôn chạy fix script sau khi regenerate
4. **Testing**: Test trên real devices, không chỉ emulator
5. **Documentation**: Update guide này khi có thay đổi configuration